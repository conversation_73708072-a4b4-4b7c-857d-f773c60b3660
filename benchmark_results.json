[{"test_name": "Protocol State Transitions", "duration_ms": 3, "operations_per_second": 271791.49461132596, "memory_usage_mb": 50, "success_rate": 1.0, "latency_stats": {"min_ms": 0, "max_ms": 0, "avg_ms": 0, "p50_ms": 0, "p95_ms": 0, "p99_ms": 0}, "error_count": 0}, {"test_name": "Device Identification", "duration_ms": 9, "operations_per_second": 1028974.2690578381, "memory_usage_mb": 50, "success_rate": 1.0, "latency_stats": {"min_ms": 0, "max_ms": 0, "avg_ms": 0, "p50_ms": 0, "p95_ms": 0, "p99_ms": 0}, "error_count": 0}, {"test_name": "Message Serialization", "duration_ms": 588, "operations_per_second": 84890.2800685349, "memory_usage_mb": 50, "success_rate": 1.0, "latency_stats": {"min_ms": 0, "max_ms": 0, "avg_ms": 0, "p50_ms": 0, "p95_ms": 0, "p99_ms": 0}, "error_count": 0}, {"test_name": "Concurrent Protocol Handling", "duration_ms": 0, "operations_per_second": 321844.5555165766, "memory_usage_mb": 50, "success_rate": 1.0, "latency_stats": {"min_ms": 0, "max_ms": 0, "avg_ms": 0, "p50_ms": 0, "p95_ms": 0, "p99_ms": 0}, "error_count": 0}, {"test_name": "Memory Allocation", "duration_ms": 43, "operations_per_second": 22899.03684132151, "memory_usage_mb": 50, "success_rate": 1.0, "latency_stats": {"min_ms": 0, "max_ms": 0, "avg_ms": 0, "p50_ms": 0, "p95_ms": 0, "p99_ms": 0}, "error_count": 0}]