use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Protocol metrics for monitoring and observability
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProtocolMetrics {
    /// Number of currently active connections
    pub active_connections: usize,
    /// Total number of connections since startup
    pub total_connections: u64,
    /// Total number of messages sent
    pub messages_sent: u64,
    /// Total number of messages received
    pub messages_received: u64,
    /// Errors by type
    pub errors_by_type: HashMap<String, u64>,
    /// Average device identification time in milliseconds
    pub average_identification_time_ms: u64,
    /// Connection success rate (0.0 to 1.0)
    pub connection_success_rate: f64,
    /// Average message latency in milliseconds
    pub average_message_latency_ms: u64,
    /// Peak memory usage in MB
    pub peak_memory_usage_mb: u64,
    /// Uptime in seconds
    pub uptime_seconds: u64,
    /// Last updated timestamp
    pub last_updated: String,
}

impl Default for ProtocolMetrics {
    fn default() -> Self {
        Self {
            active_connections: 0,
            total_connections: 0,
            messages_sent: 0,
            messages_received: 0,
            errors_by_type: HashMap::new(),
            average_identification_time_ms: 0,
            connection_success_rate: 1.0,
            average_message_latency_ms: 0,
            peak_memory_usage_mb: 0,
            uptime_seconds: 0,
            last_updated: chrono::Utc::now().to_rfc3339(),
        }
    }
}

/// Connection statistics for individual connections
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStats {
    pub connection_id: u64,
    pub state: String,
    pub device_id: Option<String>,
    pub ip_address: String,
    pub connected_at: String,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub last_activity: String,
    pub identification_time_ms: Option<u64>,
}

/// Metrics collector for protocol performance monitoring
pub struct ProtocolMetricsCollector {
    metrics: Arc<RwLock<ProtocolMetrics>>,
    connection_stats: Arc<RwLock<HashMap<u64, ConnectionStats>>>,
    start_time: Instant,
    identification_times: Arc<RwLock<Vec<u64>>>,
    message_latencies: Arc<RwLock<Vec<u64>>>,
}

impl ProtocolMetricsCollector {
    /// Create a new metrics collector
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(ProtocolMetrics::default())),
            connection_stats: Arc::new(RwLock::new(HashMap::new())),
            start_time: Instant::now(),
            identification_times: Arc::new(RwLock::new(Vec::new())),
            message_latencies: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Record a new connection
    pub async fn record_connection(&self, connection_id: u64, ip_address: String) {
        let mut metrics = self.metrics.write().await;
        let mut stats = self.connection_stats.write().await;

        metrics.active_connections += 1;
        metrics.total_connections += 1;

        let connection_stats = ConnectionStats {
            connection_id,
            state: "AwaitDeviceHello".to_string(),
            device_id: None,
            ip_address,
            connected_at: chrono::Utc::now().to_rfc3339(),
            messages_sent: 0,
            messages_received: 0,
            last_activity: chrono::Utc::now().to_rfc3339(),
            identification_time_ms: None,
        };

        stats.insert(connection_id, connection_stats);
        self.update_timestamp(&mut metrics).await;
    }

    /// Record connection disconnection
    pub async fn record_disconnection(&self, connection_id: u64) {
        let mut metrics = self.metrics.write().await;
        let mut stats = self.connection_stats.write().await;

        if metrics.active_connections > 0 {
            metrics.active_connections -= 1;
        }

        stats.remove(&connection_id);
        self.update_timestamp(&mut metrics).await;
    }

    /// Record state transition
    pub async fn record_state_transition(&self, connection_id: u64, new_state: String) {
        let mut stats = self.connection_stats.write().await;
        
        if let Some(connection_stats) = stats.get_mut(&connection_id) {
            connection_stats.state = new_state;
            connection_stats.last_activity = chrono::Utc::now().to_rfc3339();
        }
    }

    /// Record device identification
    pub async fn record_device_identification(&self, connection_id: u64, device_id: String, identification_time: Duration) {
        let mut stats = self.connection_stats.write().await;
        let mut identification_times = self.identification_times.write().await;

        let identification_time_ms = identification_time.as_millis() as u64;
        identification_times.push(identification_time_ms);

        // Keep only the last 1000 identification times for average calculation
        if identification_times.len() > 1000 {
            identification_times.remove(0);
        }

        if let Some(connection_stats) = stats.get_mut(&connection_id) {
            connection_stats.device_id = Some(device_id);
            connection_stats.identification_time_ms = Some(identification_time_ms);
            connection_stats.last_activity = chrono::Utc::now().to_rfc3339();
        }

        // Update average identification time
        let mut metrics = self.metrics.write().await;
        metrics.average_identification_time_ms = identification_times.iter().sum::<u64>() / identification_times.len() as u64;
        self.update_timestamp(&mut metrics).await;
    }

    /// Record message sent
    pub async fn record_message_sent(&self, connection_id: u64, latency: Option<Duration>) {
        let mut metrics = self.metrics.write().await;
        let mut stats = self.connection_stats.write().await;

        metrics.messages_sent += 1;

        if let Some(connection_stats) = stats.get_mut(&connection_id) {
            connection_stats.messages_sent += 1;
            connection_stats.last_activity = chrono::Utc::now().to_rfc3339();
        }

        // Record latency if provided
        if let Some(latency) = latency {
            let mut latencies = self.message_latencies.write().await;
            let latency_ms = latency.as_millis() as u64;
            latencies.push(latency_ms);

            // Keep only the last 1000 latencies for average calculation
            if latencies.len() > 1000 {
                latencies.remove(0);
            }

            metrics.average_message_latency_ms = latencies.iter().sum::<u64>() / latencies.len() as u64;
        }

        self.update_timestamp(&mut metrics).await;
    }

    /// Record message received
    pub async fn record_message_received(&self, connection_id: u64) {
        let mut metrics = self.metrics.write().await;
        let mut stats = self.connection_stats.write().await;

        metrics.messages_received += 1;

        if let Some(connection_stats) = stats.get_mut(&connection_id) {
            connection_stats.messages_received += 1;
            connection_stats.last_activity = chrono::Utc::now().to_rfc3339();
        }

        self.update_timestamp(&mut metrics).await;
    }

    /// Record an error
    pub async fn record_error(&self, error_type: String) {
        let mut metrics = self.metrics.write().await;
        
        *metrics.errors_by_type.entry(error_type).or_insert(0) += 1;
        self.update_timestamp(&mut metrics).await;
    }

    /// Update memory usage
    pub async fn update_memory_usage(&self, memory_usage_mb: u64) {
        let mut metrics = self.metrics.write().await;
        
        if memory_usage_mb > metrics.peak_memory_usage_mb {
            metrics.peak_memory_usage_mb = memory_usage_mb;
        }

        self.update_timestamp(&mut metrics).await;
    }

    /// Get current metrics snapshot
    pub async fn get_metrics(&self) -> ProtocolMetrics {
        let mut metrics = self.metrics.read().await.clone();
        
        // Update uptime
        metrics.uptime_seconds = self.start_time.elapsed().as_secs();
        
        // Calculate connection success rate
        if metrics.total_connections > 0 {
            let total_errors: u64 = metrics.errors_by_type.values().sum();
            metrics.connection_success_rate = 1.0 - (total_errors as f64 / metrics.total_connections as f64);
        }

        metrics
    }

    /// Get connection statistics
    pub async fn get_connection_stats(&self) -> HashMap<u64, ConnectionStats> {
        self.connection_stats.read().await.clone()
    }

    /// Get connection count by state
    pub async fn get_connection_count_by_state(&self) -> HashMap<String, usize> {
        let stats = self.connection_stats.read().await;
        let mut counts = HashMap::new();

        for connection_stats in stats.values() {
            *counts.entry(connection_stats.state.clone()).or_insert(0) += 1;
        }

        counts
    }

    /// Reset metrics (useful for testing)
    pub async fn reset(&self) {
        let mut metrics = self.metrics.write().await;
        let mut stats = self.connection_stats.write().await;
        let mut identification_times = self.identification_times.write().await;
        let mut latencies = self.message_latencies.write().await;

        *metrics = ProtocolMetrics::default();
        stats.clear();
        identification_times.clear();
        latencies.clear();
    }

    /// Update timestamp helper
    async fn update_timestamp(&self, metrics: &mut ProtocolMetrics) {
        metrics.last_updated = chrono::Utc::now().to_rfc3339();
    }
}

impl Default for ProtocolMetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_metrics_collection() {
        let collector = ProtocolMetricsCollector::new();

        // Test connection recording
        collector.record_connection(1, "127.0.0.1".to_string()).await;
        collector.record_connection(2, "192.168.1.1".to_string()).await;

        let metrics = collector.get_metrics().await;
        assert_eq!(metrics.active_connections, 2);
        assert_eq!(metrics.total_connections, 2);

        // Test state transition
        collector.record_state_transition(1, "IdentifyingDevice".to_string()).await;

        let stats = collector.get_connection_stats().await;
        assert_eq!(stats.get(&1).unwrap().state, "IdentifyingDevice");

        // Test device identification
        collector.record_device_identification(1, "device-1".to_string(), Duration::from_millis(100)).await;

        let metrics = collector.get_metrics().await;
        assert_eq!(metrics.average_identification_time_ms, 100);

        // Test message recording
        collector.record_message_sent(1, Some(Duration::from_millis(50))).await;
        collector.record_message_received(1).await;

        let metrics = collector.get_metrics().await;
        assert_eq!(metrics.messages_sent, 1);
        assert_eq!(metrics.messages_received, 1);
        assert_eq!(metrics.average_message_latency_ms, 50);

        // Test error recording
        collector.record_error("DEVICE_NOT_IDENTIFIED".to_string()).await;

        let metrics = collector.get_metrics().await;
        assert_eq!(metrics.errors_by_type.get("DEVICE_NOT_IDENTIFIED"), Some(&1));

        // Test disconnection
        collector.record_disconnection(1).await;

        let metrics = collector.get_metrics().await;
        assert_eq!(metrics.active_connections, 1);
    }

    #[tokio::test]
    async fn test_connection_count_by_state() {
        let collector = ProtocolMetricsCollector::new();

        collector.record_connection(1, "127.0.0.1".to_string()).await;
        collector.record_connection(2, "127.0.0.1".to_string()).await;
        collector.record_state_transition(1, "StreamingData".to_string()).await;

        let counts = collector.get_connection_count_by_state().await;
        assert_eq!(counts.get("AwaitDeviceHello"), Some(&1));
        assert_eq!(counts.get("StreamingData"), Some(&1));
    }
}
