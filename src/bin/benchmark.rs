use std::sync::Arc;
use anyhow::Result;
use tempfile::NamedTempFile;
use std::fs;
use serde_json::json;

use onboard_data_server::device_identification::DeviceIdentificationService;
use onboard_data_server::performance_benchmarks::PerformanceBenchmarks;

/// Create test device configuration for benchmarks
fn create_benchmark_device_config() -> String {
    json!({
        "devices": [
            {
                "lookup": {
                    "ip": "127.0.0.1"
                },
                "device_id": "benchmark-device",
                "device_classifier": "onboard-display-information"
            },
            {
                "lookup": {
                    "ip": "***********"
                },
                "device_id": "benchmark-device-2",
                "device_classifier": "onboard-display-information"
            }
        ]
    }).to_string()
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("Onboard Display Protocol Performance Benchmarks");
    println!("===============================================");

    // Create temporary device config for benchmarks
    let temp_file = NamedTempFile::new()?;
    let config_content = create_benchmark_device_config();
    fs::write(temp_file.path(), config_content)?;

    // Initialize device identification service
    let identification_service = Arc::new(
        DeviceIdentificationService::from_config_file(temp_file.path().to_str().unwrap())?
    );

    // Create and run benchmarks
    let mut benchmarks = PerformanceBenchmarks::new(identification_service);
    
    println!("Running comprehensive performance benchmarks...\n");
    benchmarks.run_all_benchmarks().await?;

    // Print summary
    benchmarks.print_summary();

    // Export results to JSON
    match benchmarks.export_to_json() {
        Ok(json_results) => {
            fs::write("benchmark_results.json", json_results)?;
            println!("\nResults exported to benchmark_results.json");
        }
        Err(e) => {
            eprintln!("Failed to export results: {}", e);
        }
    }

    println!("\nBenchmarks completed successfully!");
    Ok(())
}
