use std::sync::Arc;
use std::time::Instant;
use tokio::sync::mpsc;
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};

use crate::device_identification::{DeviceIdentificationService, ConnectionMetadata};
use crate::onboard_display_protocol::OnboardDisplayProtocol;
use crate::onboard_model::{OnboardData, VehicleInformation, DoorStatus};

/// Performance benchmark results
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BenchmarkResults {
    pub test_name: String,
    pub duration_ms: u64,
    pub operations_per_second: f64,
    pub memory_usage_mb: u64,
    pub success_rate: f64,
    pub latency_stats: LatencyStats,
    pub error_count: u64,
}

/// Latency statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LatencyStats {
    pub min_ms: u64,
    pub max_ms: u64,
    pub avg_ms: u64,
    pub p50_ms: u64,
    pub p95_ms: u64,
    pub p99_ms: u64,
}

/// Performance benchmark suite
pub struct PerformanceBenchmarks {
    identification_service: Arc<DeviceIdentificationService>,
    results: Vec<BenchmarkResults>,
}

impl PerformanceBenchmarks {
    /// Create new benchmark suite
    pub fn new(identification_service: Arc<DeviceIdentificationService>) -> Self {
        Self {
            identification_service,
            results: Vec::new(),
        }
    }

    /// Run all benchmarks
    pub async fn run_all_benchmarks(&mut self) -> Result<()> {
        println!("Starting comprehensive performance benchmarks...");

        // Protocol state transition benchmark
        self.benchmark_protocol_state_transitions().await?;

        // Device identification benchmark
        self.benchmark_device_identification().await?;

        // Message serialization benchmark
        self.benchmark_message_serialization().await?;

        // Concurrent protocol handling benchmark
        self.benchmark_concurrent_protocols().await?;

        // Memory allocation benchmark
        self.benchmark_memory_allocation().await?;

        println!("All benchmarks completed!");
        Ok(())
    }

    /// Benchmark protocol state transitions
    async fn benchmark_protocol_state_transitions(&mut self) -> Result<()> {
        let test_name = "Protocol State Transitions";
        println!("Running benchmark: {}", test_name);

        let iterations = 1000;
        let mut latencies = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        let start_time = Instant::now();

        for i in 0..iterations {
            let iteration_start = Instant::now();

            // Create protocol instance
            let (protocol_tx, _protocol_rx) = mpsc::unbounded_channel();
            let connection_metadata = ConnectionMetadata {
                ip: "127.0.0.1".parse().unwrap(),
                display_id: Some(format!("bench-display-{}", i)),
                dns: None,
            };

            let mut protocol = OnboardDisplayProtocol::new(i as u64, connection_metadata, protocol_tx);

            // Measure state transition time
            match protocol.handle_hello_timeout(self.identification_service.clone()).await {
                Ok(_) => {
                    success_count += 1;
                    let latency = iteration_start.elapsed().as_millis() as u64;
                    latencies.push(latency);
                }
                Err(_) => error_count += 1,
            }
        }

        let total_duration = start_time.elapsed();
        let latency_stats = self.calculate_latency_stats(&latencies);

        let results = BenchmarkResults {
            test_name: test_name.to_string(),
            duration_ms: total_duration.as_millis() as u64,
            operations_per_second: success_count as f64 / total_duration.as_secs_f64(),
            memory_usage_mb: self.estimate_memory_usage(),
            success_rate: success_count as f64 / iterations as f64,
            latency_stats,
            error_count,
        };

        println!("  Operations/sec: {:.2}", results.operations_per_second);
        println!("  Success rate: {:.2}%", results.success_rate * 100.0);
        println!("  Avg latency: {}ms", results.latency_stats.avg_ms);

        self.results.push(results);
        Ok(())
    }

    /// Benchmark device identification performance
    async fn benchmark_device_identification(&mut self) -> Result<()> {
        let test_name = "Device Identification";
        println!("Running benchmark: {}", test_name);

        let iterations = 10000;
        let mut latencies = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        let start_time = Instant::now();

        for i in 0..iterations {
            let iteration_start = Instant::now();

            let connection_metadata = ConnectionMetadata {
                ip: "127.0.0.1".parse().unwrap(),
                display_id: Some(format!("bench-display-{}", i % 100)), // Cycle through 100 IDs
                dns: None,
            };

            match self.identification_service.identify_device(&connection_metadata) {
                Ok(_) => {
                    success_count += 1;
                    let latency = iteration_start.elapsed().as_micros() as u64 / 1000; // Convert to ms
                    latencies.push(latency);
                }
                Err(_) => error_count += 1,
            }
        }

        let total_duration = start_time.elapsed();
        let latency_stats = self.calculate_latency_stats(&latencies);

        let results = BenchmarkResults {
            test_name: test_name.to_string(),
            duration_ms: total_duration.as_millis() as u64,
            operations_per_second: success_count as f64 / total_duration.as_secs_f64(),
            memory_usage_mb: self.estimate_memory_usage(),
            success_rate: success_count as f64 / iterations as f64,
            latency_stats,
            error_count,
        };

        println!("  Operations/sec: {:.2}", results.operations_per_second);
        println!("  Success rate: {:.2}%", results.success_rate * 100.0);
        println!("  Avg latency: {}ms", results.latency_stats.avg_ms);

        self.results.push(results);
        Ok(())
    }

    /// Benchmark message serialization performance
    async fn benchmark_message_serialization(&mut self) -> Result<()> {
        let test_name = "Message Serialization";
        println!("Running benchmark: {}", test_name);

        let iterations = 50000;
        let mut latencies = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        // Create test data
        let test_data = self.create_test_onboard_data();

        let start_time = Instant::now();

        for _ in 0..iterations {
            let iteration_start = Instant::now();

            // Serialize and deserialize
            match serde_json::to_string(&test_data) {
                Ok(json_str) => {
                    match serde_json::from_str::<OnboardData>(&json_str) {
                        Ok(_) => {
                            success_count += 1;
                            let latency = iteration_start.elapsed().as_micros() as u64 / 1000;
                            latencies.push(latency);
                        }
                        Err(_) => error_count += 1,
                    }
                }
                Err(_) => error_count += 1,
            }
        }

        let total_duration = start_time.elapsed();
        let latency_stats = self.calculate_latency_stats(&latencies);

        let results = BenchmarkResults {
            test_name: test_name.to_string(),
            duration_ms: total_duration.as_millis() as u64,
            operations_per_second: success_count as f64 / total_duration.as_secs_f64(),
            memory_usage_mb: self.estimate_memory_usage(),
            success_rate: success_count as f64 / iterations as f64,
            latency_stats,
            error_count,
        };

        println!("  Operations/sec: {:.2}", results.operations_per_second);
        println!("  Success rate: {:.2}%", results.success_rate * 100.0);
        println!("  Avg latency: {}ms", results.latency_stats.avg_ms);

        self.results.push(results);
        Ok(())
    }

    /// Benchmark concurrent protocol handling
    async fn benchmark_concurrent_protocols(&mut self) -> Result<()> {
        let test_name = "Concurrent Protocol Handling";
        println!("Running benchmark: {}", test_name);

        let concurrent_protocols = 100;
        let mut latencies = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        let start_time = Instant::now();

        // Create multiple protocol instances concurrently
        let mut handles = Vec::new();
        for i in 0..concurrent_protocols {
            let service = self.identification_service.clone();
            let handle = tokio::spawn(async move {
                let iteration_start = Instant::now();

                let (protocol_tx, _protocol_rx) = mpsc::unbounded_channel();
                let connection_metadata = ConnectionMetadata {
                    ip: "127.0.0.1".parse().unwrap(),
                    display_id: Some(format!("concurrent-{}", i)),
                    dns: None,
                };

                let mut protocol = OnboardDisplayProtocol::new(i as u64, connection_metadata, protocol_tx);

                match protocol.handle_hello_timeout(service).await {
                    Ok(_) => Ok(iteration_start.elapsed().as_millis() as u64),
                    Err(e) => Err(e),
                }
            });
            handles.push(handle);
        }

        // Wait for all to complete
        for handle in handles {
            match handle.await {
                Ok(Ok(latency)) => {
                    success_count += 1;
                    latencies.push(latency);
                }
                _ => error_count += 1,
            }
        }

        let total_duration = start_time.elapsed();
        let latency_stats = self.calculate_latency_stats(&latencies);

        let results = BenchmarkResults {
            test_name: test_name.to_string(),
            duration_ms: total_duration.as_millis() as u64,
            operations_per_second: success_count as f64 / total_duration.as_secs_f64(),
            memory_usage_mb: self.estimate_memory_usage(),
            success_rate: success_count as f64 / concurrent_protocols as f64,
            latency_stats,
            error_count,
        };

        println!("  Operations/sec: {:.2}", results.operations_per_second);
        println!("  Success rate: {:.2}%", results.success_rate * 100.0);
        println!("  Avg latency: {}ms", results.latency_stats.avg_ms);

        self.results.push(results);
        Ok(())
    }

    /// Benchmark memory allocation patterns
    async fn benchmark_memory_allocation(&mut self) -> Result<()> {
        let test_name = "Memory Allocation";
        println!("Running benchmark: {}", test_name);

        let iterations = 1000;
        let mut latencies = Vec::new();
        let success_count = iterations; // This test always succeeds

        let start_time = Instant::now();

        for i in 0..iterations {
            let iteration_start = Instant::now();

            // Allocate and deallocate various data structures
            let _protocols: Vec<_> = (0..10).map(|j| {
                let (protocol_tx, _protocol_rx) = mpsc::unbounded_channel();
                let connection_metadata = ConnectionMetadata {
                    ip: "127.0.0.1".parse().unwrap(),
                    display_id: Some(format!("alloc-{}-{}", i, j)),
                    dns: None,
                };
                OnboardDisplayProtocol::new((i * 10 + j) as u64, connection_metadata, protocol_tx)
            }).collect();

            let _data_vec: Vec<_> = (0..100).map(|_| self.create_test_onboard_data()).collect();

            let latency = iteration_start.elapsed().as_micros() as u64 / 1000;
            latencies.push(latency);

            // Force deallocation
            drop(_protocols);
            drop(_data_vec);
        }

        let total_duration = start_time.elapsed();
        let latency_stats = self.calculate_latency_stats(&latencies);

        let results = BenchmarkResults {
            test_name: test_name.to_string(),
            duration_ms: total_duration.as_millis() as u64,
            operations_per_second: success_count as f64 / total_duration.as_secs_f64(),
            memory_usage_mb: self.estimate_memory_usage(),
            success_rate: 1.0,
            latency_stats,
            error_count: 0,
        };

        println!("  Operations/sec: {:.2}", results.operations_per_second);
        println!("  Success rate: {:.2}%", results.success_rate * 100.0);
        println!("  Avg latency: {}ms", results.latency_stats.avg_ms);

        self.results.push(results);
        Ok(())
    }

    /// Calculate latency statistics
    fn calculate_latency_stats(&self, latencies: &[u64]) -> LatencyStats {
        if latencies.is_empty() {
            return LatencyStats {
                min_ms: 0,
                max_ms: 0,
                avg_ms: 0,
                p50_ms: 0,
                p95_ms: 0,
                p99_ms: 0,
            };
        }

        let mut sorted = latencies.to_vec();
        sorted.sort_unstable();

        let len = sorted.len();
        let sum: u64 = sorted.iter().sum();

        LatencyStats {
            min_ms: sorted[0],
            max_ms: sorted[len - 1],
            avg_ms: sum / len as u64,
            p50_ms: sorted[len / 2],
            p95_ms: sorted[(len * 95) / 100],
            p99_ms: sorted[(len * 99) / 100],
        }
    }

    /// Estimate current memory usage (simplified)
    fn estimate_memory_usage(&self) -> u64 {
        // This is a simplified estimation
        // In a real implementation, you might use system calls or memory profiling
        50 // Placeholder: 50MB
    }

    /// Create test onboard data
    fn create_test_onboard_data(&self) -> OnboardData {
        use jiff::Timestamp;
        
        OnboardData {
            current_time: Timestamp::now(),
            vehicle_information: VehicleInformation {
                vehicle_id: "benchmark-vehicle".to_string(),
                coach_number: Some("1".to_string()),
                door_status: DoorStatus::Closed,
            },
            vehicle_journey: None,
        }
    }

    /// Get benchmark results
    pub fn get_results(&self) -> &[BenchmarkResults] {
        &self.results
    }

    /// Print summary report
    pub fn print_summary(&self) {
        println!("\n========================================");
        println!("Performance Benchmark Summary");
        println!("========================================");

        for result in &self.results {
            println!("\n{}", result.test_name);
            println!("  Duration: {}ms", result.duration_ms);
            println!("  Operations/sec: {:.2}", result.operations_per_second);
            println!("  Success rate: {:.2}%", result.success_rate * 100.0);
            println!("  Memory usage: {}MB", result.memory_usage_mb);
            println!("  Latency (avg/p95/p99): {}ms/{}ms/{}ms", 
                result.latency_stats.avg_ms,
                result.latency_stats.p95_ms,
                result.latency_stats.p99_ms
            );
            if result.error_count > 0 {
                println!("  Errors: {}", result.error_count);
            }
        }

        println!("\n========================================");
    }

    /// Export results to JSON
    pub fn export_to_json(&self) -> Result<String> {
        serde_json::to_string_pretty(&self.results)
            .map_err(|e| anyhow!("Failed to serialize results: {}", e))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use std::fs;
    use serde_json::json;

    fn create_test_device_config() -> String {
        json!({
            "devices": [
                {
                    "lookup": {
                        "ip": "127.0.0.1"
                    },
                    "device_id": "benchmark-device",
                    "device_classifier": "onboard-display-information"
                }
            ]
        }).to_string()
    }

    #[tokio::test]
    async fn test_benchmark_suite() {
        let temp_file = NamedTempFile::new().unwrap();
        let config_content = create_test_device_config();
        fs::write(temp_file.path(), config_content).unwrap();

        let identification_service = Arc::new(
            DeviceIdentificationService::from_config_file(temp_file.path().to_str().unwrap()).unwrap()
        );

        let mut benchmarks = PerformanceBenchmarks::new(identification_service);
        
        // Run a subset of benchmarks for testing
        benchmarks.benchmark_device_identification().await.unwrap();
        benchmarks.benchmark_message_serialization().await.unwrap();

        let results = benchmarks.get_results();
        assert_eq!(results.len(), 2);
        
        // Verify results have reasonable values
        for result in results {
            assert!(result.operations_per_second > 0.0);
            assert!(result.success_rate > 0.0);
            assert!(result.duration_ms > 0);
        }
    }
}
