use clap::Parser;
use tracing::{info, instrument, warn};
use anyhow::{Context, Result};
use tokio::sync::mpsc;

use crate::onboard_model::OnboardDataMessage;
// Import the necessary structs and conversion target
use crate::onboard_model::OnboardData;
use crate::cli::DataSource; // Import the DataSource enum

mod logging;
mod cli;
mod service;
mod onboard_model;
mod from_baselink;
mod baselink_api_client;
mod baselink_data_fetcher;
mod journey_simulation; // Add the new onboard modifications module
mod journey_timeline; // Add the new timeline module
mod onboard_protocol;
mod data_coordinator;
mod http_api; // HTTP API module
mod http_data_fetcher; // HTTP data fetcher module
mod simulation_manager; // High-level simulation manager
mod device_identification; // Device identification service for protocol
mod onboard_display_protocol; // Protocol state machine for Phase 2
mod protocol_manager; // Protocol manager for handling multiple connections
mod performance_benchmarks; // Performance benchmarking suite for Phase 3
mod protocol_metrics; // Protocol metrics and monitoring for Phase 3

// Mark the main function as async using tokio::main
#[tokio::main]
#[instrument]
async fn main() -> Result<()> {
    let cli = cli::Cli::parse();
    
    // Setup logging
    crate::logging::setup_logging(&cli.log_format);
    
    info!("Starting server with CLI arguments: {:?}", cli);

    // Check if demo mode is requested
    if cli.demo {
        info!("Demo mode requested but not implemented in production build");
        return Ok(());
    }

    // --- Data Fetcher Setup ---
    // Create channels for the data pipeline:
    // 1. Raw data from fetchers to coordinator
    let (raw_data_tx, raw_data_rx) = mpsc::channel::<OnboardDataMessage>(10);
    // 2. Processed data from coordinator to server
    let (processed_data_tx, processed_data_rx) = mpsc::unbounded_channel::<OnboardData>();

    // --- Data Coordinator Setup ---
    // Spawn the data coordinator task
    tokio::spawn(data_coordinator::run_data_coordinator(
        raw_data_rx,
        processed_data_tx,
    ));
    info!("Data coordinator task spawned.");

    // Initialize baselink API if baselink_url is provided
    // This is needed for both modes:
    // - Baselink mode: for data fetching
    // - HTTP mode: for HTTP API endpoints
    let baselink_api = if !cli.baselink_url.is_empty() {
        info!("Initializing Baselink API connection to: {}", cli.baselink_url);
        
        match baselink_api_client::BaselinkApi::new_connected(
            &cli.baselink_url,
            "onboard-display-information".to_string(),
        )
        .await
        {
            Ok(baselink) => {
                info!(
                    "Connected to Baselink Server version: {:?}",
                    baselink.get_version().await?
                );

                let response = baselink.get_vehicles().await?;
                info!("Vehicles: {:?}", response);

                Some(baselink)
            }
            Err(e) => {
                match cli.data_source {
                    DataSource::Baselink => {
                        // In Baselink mode, connection failure is fatal
                        return Err(e).context("Failed to connect to baselink (required for Baselink data source)");
                    }
                    DataSource::Http => {
                        // In HTTP mode, baselink is optional for API endpoints
                        warn!("Failed to connect to baselink (HTTP API baselink endpoints will be unavailable): {}", e);
                        None
                    }
                }
            }
        }
    } else {
        match cli.data_source {
            DataSource::Baselink => {
                return Err(anyhow::anyhow!("Baselink URL is required when using Baselink data source"));
            }
            DataSource::Http => {
                info!("No Baselink URL provided - HTTP API baselink endpoints will be unavailable");
                None
            }
        }
    };

    // Create the HTTP server with optional baselink API
    let (http_app, server_state) = http_api::create_http_server(baselink_api.clone());

    // Conditionally start the appropriate data fetcher
    match cli.data_source {
        DataSource::Baselink => {
            info!("Using Baselink data source for data fetching.");
            
            // Baselink API must be available for Baselink mode
            let baselink_api = baselink_api.ok_or_else(|| {
                anyhow::anyhow!("Baselink API not initialized but required for Baselink data source")
            })?;

            // Configure the Baselink data fetcher
            let fetcher_config = baselink_data_fetcher::DataFetcherConfig {
                baselink_api,
                vehicle_id: cli.vehicle_id.clone(),
                fetch_interval_seconds: cli.fetch_interval_seconds,
            };

            // Spawn the Baselink data fetcher task
            tokio::spawn(baselink_data_fetcher::run_data_fetcher(fetcher_config, raw_data_tx));
            info!("Baselink data fetcher task spawned.");
        }
        DataSource::Http => {
            info!("Using HTTP API data source with simulation data.");
            
            // Configure the HTTP data fetcher
            let fetcher_config = http_data_fetcher::HttpDataFetcherConfig {
                simulation_state: server_state.simulation_manager.clone(),
                vehicle_id: cli.vehicle_id.clone(),
                fetch_interval_seconds: cli.fetch_interval_seconds,
            };

            // Spawn the HTTP data fetcher task
            tokio::spawn(http_data_fetcher::run_http_data_fetcher(fetcher_config, raw_data_tx));
            info!("HTTP data fetcher task spawned.");
        }
    }

    // Start the HTTP server
    let http_addr = "127.0.0.1:8080"; // Different port from WebSocket
    info!(address = %http_addr, "Starting HTTP API server");
    tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(http_addr).await.unwrap();
        axum::serve(listener, http_app).await.unwrap();
    });
    info!("HTTP API server started on {}", http_addr);

    // --- WebSocket Server Setup ---
    let addr = cli.addr;
    info!(address = %addr, "Starting WebSocket server");
    // Pass the receiver end of the processed data channel to the server
    service::start_server(&addr, processed_data_rx).await?; // Pass processed_data_rx to start_server

    info!("Server shutdown initiated");
    Ok(())
}

// device_manager = DeviceManager::new()

// source_protocol = instantiate source_protocol

// source_stream = source_protocol.stream()


// core =  init(source_stream)







// trait DataSource {
//     fn fetch_data(&self) -> Result<OnboardData>;
// }

// trait DataOutput {
//     fn send_data(&self, data: OnboardData);
// }




