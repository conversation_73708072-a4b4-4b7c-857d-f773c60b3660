// Public modules for library usage
pub mod device_identification;
pub mod onboard_display_protocol;
pub mod onboard_model;
pub mod onboard_protocol;
pub mod performance_benchmarks;
pub mod protocol_manager;
pub mod protocol_metrics;

// Internal modules
mod logging;
mod cli;
mod service;
mod from_baselink;
mod baselink_api_client;
mod baselink_data_fetcher;
mod journey_simulation;
mod journey_timeline;
mod data_coordinator;
mod http_api;
mod http_data_fetcher;
mod simulation_manager;
