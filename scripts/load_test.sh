#!/bin/bash

# Load Testing Script for Onboard Display Protocol
# Tests high-connection scenarios and performance under load

set -e

# Configuration
SERVER_URL="ws://127.0.0.1:9001"
SERVER_ADDR="127.0.0.1:9001"
MAX_CONNECTIONS=100
RAMP_UP_DELAY=0.01  # Delay between connection attempts
TEST_DURATION=60    # Test duration in seconds
MEMORY_CHECK_INTERVAL=5  # Check memory every 5 seconds

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Metrics
CONNECTIONS_ESTABLISHED=0
CONNECTIONS_FAILED=0
TOTAL_MESSAGES_RECEIVED=0
START_TIME=0
END_TIME=0

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Check dependencies
check_dependencies() {
    if ! command -v websocat &> /dev/null; then
        log_error "websocat is required but not installed"
        exit 1
    fi
    
    if ! command -v ps &> /dev/null; then
        log_error "ps command is required but not available"
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# Start test server
start_server() {
    log_info "Starting test server for load testing..."
    cargo run -- --data-source http --vehicle-id "load-test-vehicle" --addr "$SERVER_ADDR" &
    SERVER_PID=$!
    
    sleep 3
    
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Failed to start test server"
        exit 1
    fi
    
    log_success "Test server started (PID: $SERVER_PID)"
}

# Stop test server
stop_server() {
    if [ ! -z "$SERVER_PID" ]; then
        log_info "Stopping test server..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        log_success "Test server stopped"
    fi
}

# Monitor server memory usage
monitor_memory() {
    local pid=$1
    local output_file=$2
    
    while kill -0 $pid 2>/dev/null; do
        local memory_kb=$(ps -o rss= -p $pid 2>/dev/null | tr -d ' ')
        if [ ! -z "$memory_kb" ]; then
            local memory_mb=$((memory_kb / 1024))
            echo "$(date +%s),$memory_mb" >> "$output_file"
        fi
        sleep $MEMORY_CHECK_INTERVAL
    done
}

# Single connection test
test_single_connection() {
    local connection_id=$1
    local duration=$2
    local messages_file=$3
    
    {
        local message_count=0
        timeout $duration websocat "$SERVER_URL" 2>/dev/null | while read line; do
            ((message_count++))
            echo "$connection_id,$message_count" >> "$messages_file"
        done
        echo "Connection $connection_id completed with $message_count messages"
    } &
}

# Load test with gradual ramp-up
run_load_test() {
    log_info "Starting load test with $MAX_CONNECTIONS connections"
    
    # Create temporary files for metrics
    local memory_file=$(mktemp)
    local messages_file=$(mktemp)
    local connections_file=$(mktemp)
    
    # Start memory monitoring
    monitor_memory $SERVER_PID "$memory_file" &
    local monitor_pid=$!
    
    START_TIME=$(date +%s)
    
    # Ramp up connections gradually
    local pids=()
    for i in $(seq 1 $MAX_CONNECTIONS); do
        test_single_connection $i $TEST_DURATION "$messages_file" &
        local conn_pid=$!
        pids+=($conn_pid)
        echo "$i,$conn_pid,$(date +%s)" >> "$connections_file"
        
        # Check if connection started successfully
        sleep 0.1
        if kill -0 $conn_pid 2>/dev/null; then
            ((CONNECTIONS_ESTABLISHED++))
        else
            ((CONNECTIONS_FAILED++))
        fi
        
        # Progress indicator
        if [ $((i % 10)) -eq 0 ]; then
            log_info "Established $i/$MAX_CONNECTIONS connections..."
        fi
        
        sleep $RAMP_UP_DELAY
    done
    
    log_info "All connections started. Waiting for test completion..."
    
    # Wait for test duration
    sleep $TEST_DURATION
    
    # Stop all connections
    for pid in "${pids[@]}"; do
        kill $pid 2>/dev/null || true
    done
    
    # Stop memory monitoring
    kill $monitor_pid 2>/dev/null || true
    
    END_TIME=$(date +%s)
    
    # Analyze results
    analyze_results "$memory_file" "$messages_file" "$connections_file"
    
    # Cleanup
    rm -f "$memory_file" "$messages_file" "$connections_file"
}

# Analyze test results
analyze_results() {
    local memory_file=$1
    local messages_file=$2
    local connections_file=$3
    
    log_info "Analyzing load test results..."
    
    # Connection statistics
    local success_rate=$((CONNECTIONS_ESTABLISHED * 100 / MAX_CONNECTIONS))
    echo "Connection Statistics:"
    echo "  Total attempted: $MAX_CONNECTIONS"
    echo "  Successfully established: $CONNECTIONS_ESTABLISHED"
    echo "  Failed: $CONNECTIONS_FAILED"
    echo "  Success rate: ${success_rate}%"
    
    # Memory analysis
    if [ -f "$memory_file" ] && [ -s "$memory_file" ]; then
        local max_memory=$(awk -F',' 'BEGIN{max=0} {if($2>max) max=$2} END{print max}' "$memory_file")
        local avg_memory=$(awk -F',' '{sum+=$2; count++} END{print int(sum/count)}' "$memory_file")
        echo "Memory Usage:"
        echo "  Peak memory: ${max_memory} MB"
        echo "  Average memory: ${avg_memory} MB"
        
        # Check memory thresholds
        if [ $max_memory -lt 100 ]; then
            log_success "Memory usage within acceptable limits (<100MB)"
        else
            log_warning "High memory usage detected (${max_memory}MB)"
        fi
    fi
    
    # Message throughput
    if [ -f "$messages_file" ] && [ -s "$messages_file" ]; then
        local total_messages=$(wc -l < "$messages_file")
        local test_duration_actual=$((END_TIME - START_TIME))
        local messages_per_second=$((total_messages / test_duration_actual))
        echo "Message Throughput:"
        echo "  Total messages: $total_messages"
        echo "  Test duration: ${test_duration_actual}s"
        echo "  Messages per second: $messages_per_second"
        
        TOTAL_MESSAGES_RECEIVED=$total_messages
    fi
    
    # Performance assessment
    echo
    echo "Performance Assessment:"
    if [ $success_rate -ge 90 ] && [ ${max_memory:-0} -lt 100 ]; then
        log_success "Load test PASSED - System handles $MAX_CONNECTIONS connections well"
    elif [ $success_rate -ge 80 ]; then
        log_warning "Load test PARTIAL - Some performance issues detected"
    else
        log_error "Load test FAILED - System cannot handle $MAX_CONNECTIONS connections"
    fi
}

# Stress test with burst connections
run_stress_test() {
    log_info "Running stress test with burst connections..."
    
    local burst_size=20
    local burst_count=5
    local burst_interval=2
    
    for burst in $(seq 1 $burst_count); do
        log_info "Starting burst $burst/$burst_count ($burst_size connections)"
        
        local pids=()
        for i in $(seq 1 $burst_size); do
            timeout 5 websocat "$SERVER_URL" >/dev/null 2>&1 &
            pids+=($!)
        done
        
        # Wait for burst to complete
        local success_count=0
        for pid in "${pids[@]}"; do
            if wait $pid 2>/dev/null; then
                ((success_count++))
            fi
        done
        
        log_info "Burst $burst completed: $success_count/$burst_size successful"
        sleep $burst_interval
    done
    
    log_success "Stress test completed"
}

# Connection latency test
test_connection_latency() {
    log_info "Testing connection establishment latency..."
    
    local latencies=()
    local sample_count=10
    
    for i in $(seq 1 $sample_count); do
        local start_time=$(date +%s%3N)
        timeout 2 websocat "$SERVER_URL" >/dev/null 2>&1
        local end_time=$(date +%s%3N)
        local latency=$((end_time - start_time))
        latencies+=($latency)
    done
    
    # Calculate statistics
    local total=0
    local min=999999
    local max=0
    
    for latency in "${latencies[@]}"; do
        total=$((total + latency))
        if [ $latency -lt $min ]; then min=$latency; fi
        if [ $latency -gt $max ]; then max=$latency; fi
    done
    
    local avg=$((total / sample_count))
    
    echo "Connection Latency Statistics:"
    echo "  Average: ${avg}ms"
    echo "  Minimum: ${min}ms"
    echo "  Maximum: ${max}ms"
    
    if [ $avg -lt 50 ]; then
        log_success "Connection latency excellent (<50ms average)"
    elif [ $avg -lt 100 ]; then
        log_success "Connection latency good (<100ms average)"
    else
        log_warning "Connection latency high (${avg}ms average)"
    fi
}

# Main execution
main() {
    echo "========================================"
    echo "Onboard Display Protocol Load Testing"
    echo "========================================"
    echo "Configuration:"
    echo "  Max connections: $MAX_CONNECTIONS"
    echo "  Test duration: ${TEST_DURATION}s"
    echo "  Server: $SERVER_URL"
    echo
    
    check_dependencies
    start_server
    
    # Ensure cleanup on exit
    trap stop_server EXIT
    
    # Run tests
    test_connection_latency
    echo
    run_stress_test
    echo
    run_load_test
    
    echo
    echo "========================================"
    echo "Load Test Summary"
    echo "========================================"
    echo "Test completed successfully!"
}

# Execute main function
main "$@"
