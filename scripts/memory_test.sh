#!/bin/bash

# Memory Leak Detection and Performance Testing Script
# Tests for memory leaks, performance degradation, and long-running stability

set -e

# Configuration
SERVER_URL="ws://127.0.0.1:9001"
SERVER_ADDR="127.0.0.1:9001"
TEST_DURATION=300  # 5 minutes for memory leak test
MEMORY_CHECK_INTERVAL=5  # Check memory every 5 seconds
PERFORMANCE_ITERATIONS=1000  # Number of iterations for performance test
CONNECTION_CYCLES=50  # Number of connection cycles for leak testing

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Check dependencies
check_dependencies() {
    if ! command -v websocat &> /dev/null; then
        log_error "websocat is required but not installed"
        exit 1
    fi
    
    if ! command -v ps &> /dev/null; then
        log_error "ps command is required but not available"
        exit 1
    fi
    
    if ! command -v awk &> /dev/null; then
        log_error "awk command is required but not available"
        exit 1
    fi
    
    log_success "All dependencies available"
}

# Start test server
start_server() {
    log_info "Starting test server for memory testing..."
    cargo run -- --data-source http --vehicle-id "memory-test-vehicle" --addr "$SERVER_ADDR" &
    SERVER_PID=$!
    
    sleep 3
    
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Failed to start test server"
        exit 1
    fi
    
    log_success "Test server started (PID: $SERVER_PID)"
}

# Stop test server
stop_server() {
    if [ ! -z "$SERVER_PID" ]; then
        log_info "Stopping test server..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        log_success "Test server stopped"
    fi
}

# Get memory usage in MB
get_memory_usage() {
    local pid=$1
    local memory_kb=$(ps -o rss= -p $pid 2>/dev/null | tr -d ' ')
    if [ ! -z "$memory_kb" ]; then
        echo $((memory_kb / 1024))
    else
        echo "0"
    fi
}

# Memory leak detection test
test_memory_leak_detection() {
    log_info "Starting memory leak detection test (${TEST_DURATION}s)"
    
    local memory_log=$(mktemp)
    local start_time=$(date +%s)
    local end_time=$((start_time + TEST_DURATION))
    
    # Record initial memory
    local initial_memory=$(get_memory_usage $SERVER_PID)
    echo "0,$initial_memory" > "$memory_log"
    log_info "Initial memory usage: ${initial_memory}MB"
    
    # Start memory monitoring
    {
        while [ $(date +%s) -lt $end_time ]; do
            local current_time=$(($(date +%s) - start_time))
            local memory_mb=$(get_memory_usage $SERVER_PID)
            echo "$current_time,$memory_mb" >> "$memory_log"
            sleep $MEMORY_CHECK_INTERVAL
        done
    } &
    local monitor_pid=$!
    
    # Generate load during memory test
    {
        local cycle=0
        while [ $(date +%s) -lt $end_time ]; do
            # Create and close connections to test for leaks
            for i in $(seq 1 10); do
                timeout 2 websocat "$SERVER_URL" >/dev/null 2>&1 &
            done
            
            # Wait a bit then kill any remaining connections
            sleep 1
            jobs -p | xargs -r kill 2>/dev/null || true
            
            ((cycle++))
            if [ $((cycle % 10)) -eq 0 ]; then
                local current_memory=$(get_memory_usage $SERVER_PID)
                log_info "Cycle $cycle completed, memory: ${current_memory}MB"
            fi
        done
    } &
    local load_pid=$!
    
    # Wait for test completion
    wait $monitor_pid
    kill $load_pid 2>/dev/null || true
    
    # Analyze memory usage
    local final_memory=$(get_memory_usage $SERVER_PID)
    local max_memory=$(awk -F',' 'BEGIN{max=0} {if($2>max) max=$2} END{print max}' "$memory_log")
    local avg_memory=$(awk -F',' '{sum+=$2; count++} END{print int(sum/count)}' "$memory_log")
    local memory_growth=$((final_memory - initial_memory))
    
    echo "Memory Leak Detection Results:"
    echo "  Initial memory: ${initial_memory}MB"
    echo "  Final memory: ${final_memory}MB"
    echo "  Peak memory: ${max_memory}MB"
    echo "  Average memory: ${avg_memory}MB"
    echo "  Memory growth: ${memory_growth}MB"
    
    # Check for memory leaks (growth > 50MB is concerning)
    if [ $memory_growth -lt 50 ]; then
        log_success "Memory leak test passed (growth: ${memory_growth}MB)"
    else
        log_error "Potential memory leak detected (growth: ${memory_growth}MB)"
    fi
    
    # Check peak memory usage
    if [ $max_memory -lt 200 ]; then
        log_success "Peak memory usage acceptable (${max_memory}MB)"
    else
        log_warning "High peak memory usage (${max_memory}MB)"
    fi
    
    rm -f "$memory_log"
}

# Connection lifecycle performance test
test_connection_performance() {
    log_info "Testing connection lifecycle performance"
    
    local times_file=$(mktemp)
    local success_count=0
    local total_time=0
    
    for i in $(seq 1 $PERFORMANCE_ITERATIONS); do
        local start_time=$(date +%s%3N)
        if timeout 3 websocat "$SERVER_URL" >/dev/null 2>&1; then
            local end_time=$(date +%s%3N)
            local duration=$((end_time - start_time))
            echo "$duration" >> "$times_file"
            total_time=$((total_time + duration))
            ((success_count++))
        fi
        
        # Progress indicator
        if [ $((i % 100)) -eq 0 ]; then
            log_info "Completed $i/$PERFORMANCE_ITERATIONS iterations"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        local avg_time=$((total_time / success_count))
        local min_time=$(sort -n "$times_file" | head -1)
        local max_time=$(sort -n "$times_file" | tail -1)
        local median_time=$(sort -n "$times_file" | awk 'NR==int(NR/2)+1')
        
        echo "Connection Performance Results:"
        echo "  Successful connections: $success_count/$PERFORMANCE_ITERATIONS"
        echo "  Average time: ${avg_time}ms"
        echo "  Minimum time: ${min_time}ms"
        echo "  Maximum time: ${max_time}ms"
        echo "  Median time: ${median_time}ms"
        
        # Performance thresholds
        if [ $avg_time -lt 100 ]; then
            log_success "Connection performance excellent (<100ms average)"
        elif [ $avg_time -lt 500 ]; then
            log_success "Connection performance good (<500ms average)"
        else
            log_warning "Connection performance slow (${avg_time}ms average)"
        fi
        
        # Success rate check
        local success_rate=$((success_count * 100 / PERFORMANCE_ITERATIONS))
        if [ $success_rate -ge 95 ]; then
            log_success "Connection success rate excellent (${success_rate}%)"
        elif [ $success_rate -ge 90 ]; then
            log_success "Connection success rate good (${success_rate}%)"
        else
            log_error "Connection success rate poor (${success_rate}%)"
        fi
    else
        log_error "No successful connections in performance test"
    fi
    
    rm -f "$times_file"
}

# CPU usage monitoring test
test_cpu_usage() {
    log_info "Monitoring CPU usage under load"
    
    local cpu_log=$(mktemp)
    local test_duration=60  # 1 minute CPU test
    local start_time=$(date +%s)
    local end_time=$((start_time + test_duration))
    
    # Monitor CPU usage
    {
        while [ $(date +%s) -lt $end_time ]; do
            local cpu_percent=$(ps -o %cpu= -p $SERVER_PID 2>/dev/null | tr -d ' ')
            if [ ! -z "$cpu_percent" ]; then
                echo "$cpu_percent" >> "$cpu_log"
            fi
            sleep 1
        done
    } &
    local monitor_pid=$!
    
    # Generate CPU load
    {
        while [ $(date +%s) -lt $end_time ]; do
            for i in $(seq 1 20); do
                timeout 1 websocat "$SERVER_URL" >/dev/null 2>&1 &
            done
            sleep 0.5
            jobs -p | xargs -r kill 2>/dev/null || true
        done
    } &
    local load_pid=$!
    
    wait $monitor_pid
    kill $load_pid 2>/dev/null || true
    
    # Analyze CPU usage
    if [ -s "$cpu_log" ]; then
        local avg_cpu=$(awk '{sum+=$1; count++} END{print int(sum/count)}' "$cpu_log")
        local max_cpu=$(awk 'BEGIN{max=0} {if($1>max) max=$1} END{print int(max)}' "$cpu_log")
        
        echo "CPU Usage Results:"
        echo "  Average CPU: ${avg_cpu}%"
        echo "  Peak CPU: ${max_cpu}%"
        
        if [ $avg_cpu -lt 50 ]; then
            log_success "CPU usage efficient (<50% average)"
        elif [ $avg_cpu -lt 80 ]; then
            log_success "CPU usage acceptable (<80% average)"
        else
            log_warning "High CPU usage (${avg_cpu}% average)"
        fi
    else
        log_warning "Could not measure CPU usage"
    fi
    
    rm -f "$cpu_log"
}

# Long-running stability test
test_long_running_stability() {
    log_info "Testing long-running stability (simplified)"
    
    local stability_duration=120  # 2 minutes for demo
    local start_time=$(date +%s)
    local end_time=$((start_time + stability_duration))
    local connection_count=0
    local error_count=0
    
    while [ $(date +%s) -lt $end_time ]; do
        if timeout 2 websocat "$SERVER_URL" >/dev/null 2>&1; then
            ((connection_count++))
        else
            ((error_count++))
        fi
        
        # Brief pause between connections
        sleep 0.1
    done
    
    local error_rate=$((error_count * 100 / (connection_count + error_count)))
    
    echo "Stability Test Results:"
    echo "  Test duration: ${stability_duration}s"
    echo "  Successful connections: $connection_count"
    echo "  Failed connections: $error_count"
    echo "  Error rate: ${error_rate}%"
    
    if [ $error_rate -lt 5 ]; then
        log_success "Long-running stability excellent (<5% error rate)"
    elif [ $error_rate -lt 10 ]; then
        log_success "Long-running stability good (<10% error rate)"
    else
        log_error "Long-running stability poor (${error_rate}% error rate)"
    fi
}

# Main execution
main() {
    echo "========================================"
    echo "Memory & Performance Testing Suite"
    echo "========================================"
    echo "Configuration:"
    echo "  Test duration: ${TEST_DURATION}s"
    echo "  Performance iterations: $PERFORMANCE_ITERATIONS"
    echo "  Server: $SERVER_URL"
    echo
    
    check_dependencies
    start_server
    
    # Ensure cleanup on exit
    trap stop_server EXIT
    
    # Run all tests
    test_memory_leak_detection
    echo
    test_connection_performance
    echo
    test_cpu_usage
    echo
    test_long_running_stability
    
    # Summary
    echo
    echo "========================================"
    echo "Memory & Performance Test Summary"
    echo "========================================"
    echo "Tests passed: $TESTS_PASSED"
    echo "Tests failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}All memory and performance tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some tests failed or showed warnings.${NC}"
        exit 1
    fi
}

# Execute main function
main "$@"
