#!/bin/bash

# Comprehensive Protocol Test Suite for Onboard Display Protocol
# Phase 3 - Production Readiness Testing

set -e  # Exit on any error

# Configuration
SERVER_URL="ws://127.0.0.1:9001"
SERVER_ADDR="127.0.0.1:9001"
TEST_TIMEOUT=30
LOAD_TEST_CONNECTIONS=50
TIMING_TOLERANCE_MS=100

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Utility functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

start_test() {
    ((TESTS_TOTAL++))
    log_info "Starting test: $1"
}

# Check if websocat is available
check_dependencies() {
    if ! command -v websocat &> /dev/null; then
        log_error "websocat is required but not installed. Please install it first."
        echo "Install with: cargo install websocat"
        exit 1
    fi
    
    if ! command -v timeout &> /dev/null; then
        log_error "timeout command is required but not available."
        exit 1
    fi
    
    log_success "All dependencies available"
}

# Start test server
start_server() {
    log_info "Starting test server..."
    cargo run -- --data-source http --vehicle-id "test-vehicle" --addr "$SERVER_ADDR" &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 3
    
    # Verify server is running
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Failed to start test server"
        exit 1
    fi
    
    log_success "Test server started (PID: $SERVER_PID)"
}

# Stop test server
stop_server() {
    if [ ! -z "$SERVER_PID" ]; then
        log_info "Stopping test server..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        log_success "Test server stopped"
    fi
}

# Test 1: Basic WebSocket connectivity
test_basic_connectivity() {
    start_test "Basic WebSocket Connectivity"
    
    local output
    output=$(timeout 5 websocat "$SERVER_URL" 2>&1 | head -1)
    
    if [ $? -eq 0 ] && echo "$output" | grep -q '"type"'; then
        log_success "Basic connectivity test passed"
    else
        log_error "Basic connectivity test failed: $output"
    fi
}

# Test 2: Hello message timeout precision (3 seconds ±100ms)
test_hello_timeout_precision() {
    start_test "Hello Message Timeout Precision"
    
    local start_time=$(date +%s%3N)
    timeout 5 websocat "$SERVER_URL" >/dev/null 2>&1
    local end_time=$(date +%s%3N)
    
    local elapsed=$((end_time - start_time))
    local expected=3000  # 3 seconds in milliseconds
    local tolerance=$TIMING_TOLERANCE_MS
    
    if [ $elapsed -ge $((expected - tolerance)) ] && [ $elapsed -le $((expected + tolerance)) ]; then
        log_success "Timeout precision test passed (${elapsed}ms, expected ~${expected}ms)"
    else
        log_error "Timeout precision test failed (${elapsed}ms, expected ${expected}ms ±${tolerance}ms)"
    fi
}

# Test 3: Hello message processing
test_hello_message_processing() {
    start_test "Hello Message Processing"
    
    local output
    output=$(echo '{"type":"hello","displayId":"test-display-1"}' | timeout 5 websocat "$SERVER_URL" 2>&1 | head -2)
    
    if echo "$output" | grep -q '"device-identification"'; then
        log_success "Hello message processing test passed"
    else
        log_error "Hello message processing test failed: $output"
    fi
}

# Test 4: Malformed message handling
test_malformed_messages() {
    start_test "Malformed Message Handling"
    
    # Test malformed JSON
    local output1
    output1=$(echo '{"type":"hello","displayId":"test"' | timeout 3 websocat "$SERVER_URL" 2>&1 | head -1)
    
    # Test invalid message type
    local output2
    output2=$(echo '{"type":"invalid","data":"test"}' | timeout 3 websocat "$SERVER_URL" 2>&1 | head -1)
    
    # Server should handle these gracefully (not crash)
    if [ $? -eq 0 ]; then
        log_success "Malformed message handling test passed"
    else
        log_error "Malformed message handling test failed"
    fi
}

# Test 5: Multiple concurrent connections
test_concurrent_connections() {
    start_test "Concurrent Connections (10 connections)"
    
    local pids=()
    local success_count=0
    
    # Start 10 concurrent connections
    for i in {1..10}; do
        (timeout 3 websocat "$SERVER_URL" >/dev/null 2>&1 && echo "success") &
        pids+=($!)
    done
    
    # Wait for all connections to complete
    for pid in "${pids[@]}"; do
        if wait $pid 2>/dev/null; then
            ((success_count++))
        fi
    done
    
    if [ $success_count -ge 8 ]; then  # Allow some failures
        log_success "Concurrent connections test passed ($success_count/10 successful)"
    else
        log_error "Concurrent connections test failed ($success_count/10 successful)"
    fi
}

# Test 6: Load testing with many connections
test_load_testing() {
    start_test "Load Testing ($LOAD_TEST_CONNECTIONS connections)"
    
    local pids=()
    local success_count=0
    local start_time=$(date +%s)
    
    # Start many concurrent connections
    for i in $(seq 1 $LOAD_TEST_CONNECTIONS); do
        (timeout 2 websocat "$SERVER_URL" >/dev/null 2>&1 && echo "success") &
        pids+=($!)
        
        # Add small delay to avoid overwhelming the system
        if [ $((i % 10)) -eq 0 ]; then
            sleep 0.1
        fi
    done
    
    # Wait for all connections to complete
    for pid in "${pids[@]}"; do
        if wait $pid 2>/dev/null; then
            ((success_count++))
        fi
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local success_rate=$((success_count * 100 / LOAD_TEST_CONNECTIONS))
    
    if [ $success_rate -ge 80 ]; then  # 80% success rate threshold
        log_success "Load testing passed ($success_count/$LOAD_TEST_CONNECTIONS successful, ${success_rate}%, ${duration}s)"
    else
        log_error "Load testing failed ($success_count/$LOAD_TEST_CONNECTIONS successful, ${success_rate}%, ${duration}s)"
    fi
}

# Test 7: Device identification scenarios
test_device_identification() {
    start_test "Device Identification Scenarios"
    
    # Test with displayId
    local output1
    output1=$(echo '{"type":"hello","displayId":"test-display-1"}' | timeout 5 websocat "$SERVER_URL" 2>&1)
    
    # Test without displayId (IP-only)
    local output2
    output2=$(timeout 5 websocat "$SERVER_URL" 2>&1)
    
    if echo "$output1" | grep -q '"device-identification"' && echo "$output2" | grep -q '"device-identification"'; then
        log_success "Device identification test passed"
    else
        log_error "Device identification test failed"
    fi
}

# Test 8: Bye message handling
test_bye_message() {
    start_test "Bye Message Handling"
    
    local output
    output=$(echo -e '{"type":"hello","displayId":"test-display-1"}\n{"type":"bye","reason":"testing"}' | timeout 5 websocat "$SERVER_URL" 2>&1)
    
    if echo "$output" | grep -q '"device-identification"'; then
        log_success "Bye message handling test passed"
    else
        log_error "Bye message handling test failed: $output"
    fi
}

# Performance benchmarking
run_performance_benchmark() {
    log_info "Running performance benchmarks..."
    
    # Connection establishment time
    local conn_times=()
    for i in {1..5}; do
        local start_time=$(date +%s%3N)
        timeout 2 websocat "$SERVER_URL" >/dev/null 2>&1
        local end_time=$(date +%s%3N)
        local conn_time=$((end_time - start_time))
        conn_times+=($conn_time)
    done
    
    # Calculate average connection time
    local total=0
    for time in "${conn_times[@]}"; do
        total=$((total + time))
    done
    local avg_conn_time=$((total / ${#conn_times[@]}))
    
    log_info "Average connection establishment time: ${avg_conn_time}ms"
    
    if [ $avg_conn_time -lt 100 ]; then
        log_success "Connection performance benchmark passed (<100ms)"
    else
        log_warning "Connection performance benchmark: ${avg_conn_time}ms (target: <100ms)"
    fi
}

# Main test execution
main() {
    echo "========================================"
    echo "Onboard Display Protocol Test Suite"
    echo "Phase 3 - Production Readiness Testing"
    echo "========================================"
    echo
    
    # Setup
    check_dependencies
    start_server
    
    # Ensure cleanup on exit
    trap stop_server EXIT
    
    # Run all tests
    test_basic_connectivity
    test_hello_timeout_precision
    test_hello_message_processing
    test_malformed_messages
    test_concurrent_connections
    test_device_identification
    test_bye_message
    test_load_testing
    
    # Performance benchmarks
    run_performance_benchmark
    
    # Summary
    echo
    echo "========================================"
    echo "Test Results Summary"
    echo "========================================"
    echo "Total tests: $TESTS_TOTAL"
    echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}All tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some tests failed.${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
