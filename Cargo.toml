[package]
name = "onboard-data-server"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "onboard-data-server"
path = "src/main.rs"

[[bin]]
name = "benchmark"
path = "src/bin/benchmark.rs"

[dependencies]
anyhow = "1"
clap = { version = "4", features = ["derive", "env"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
tokio-tungstenite = "0.23.1"
futures-util = "0.3"
futures = "0.3"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["time", "json", "env-filter"] }
baselink-api = { version = "0.41", registry = "connwerk-release" }
tonic = "0.13"
jiff = "0.2.10"
crossterm = { version = "0.27", features = ["event-stream"] }
ratatui = "0.26"
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }
multipart = "0.18"
tempfile = "3.8"
chrono = { version = "0.4", features = ["serde"] }


[dev-dependencies]
tokio = { version = "1", features = ["full"] }
tokio-tungstenite = "0.23.1"
anyhow = "1"
futures-util = { version = "0.3", default-features = false, features = ["sink"] }
url = "2.5"
tempfile = "3.8"
serde_json = "1"
